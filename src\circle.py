import math


class Circle:
    def __init__(self, radius):
        """Initialize a Circle with the given radius."""
        self.radius = radius

    @property
    def diameter(self):
        """Get the diameter of the circle (2 * radius)."""
        return self.radius * 2

    @diameter.setter
    def diameter(self, value):
        """Set the diameter of the circle, updating radius accordingly."""
        self.radius = value / 2

    @property
    def area(self):
        """Get the area of the circle (π * r²)."""
        return math.pi * self.radius * self.radius

    @classmethod
    def from_diameter(cls, diameter):
        """Create a Circle from a diameter value."""
        return cls(diameter / 2)

    def __str__(self):
        """Return a string representation of the circle."""
        return f"Circle with radius: {self.radius:.6f}"

    def __repr__(self):
        """Return a representation that can be used to recreate the circle."""
        return f"Circle({self.radius})"

    def __add__(self, other):
        """Add two circles by adding their radii."""
        if isinstance(other, Circle):
            return Circle(self.radius + other.radius)
        return NotImplemented

    def __mul__(self, other):
        """Multiply circle radius by a number."""
        if isinstance(other, (int, float)):
            return Circle(self.radius * other)
        return NotImplemented

    def __rmul__(self, other):
        """Right multiplication - allows number * circle."""
        return self.__mul__(other)

    def __eq__(self, other):
        """Check if two circles are equal (same radius)."""
        if isinstance(other, Circle):
            return self.radius == other.radius
        return NotImplemented

    def __lt__(self, other):
        """Check if this circle is smaller than another (by radius)."""
        if isinstance(other, Circle):
            return self.radius < other.radius
        return NotImplemented

    def __le__(self, other):
        """Check if this circle is smaller than or equal to another."""
        if isinstance(other, Circle):
            return self.radius <= other.radius
        return NotImplemented

    def __gt__(self, other):
        """Check if this circle is larger than another (by radius)."""
        if isinstance(other, Circle):
            return self.radius > other.radius
        return NotImplemented

    def __ge__(self, other):
        """Check if this circle is larger than or equal to another."""
        if isinstance(other, Circle):
            return self.radius >= other.radius
        return NotImplemented
