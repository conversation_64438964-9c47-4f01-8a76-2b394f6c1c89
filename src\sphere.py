import math
from src.circle import Circle


class Sphere(Circle):

    @property
    def volume(self):
        return (4/3) * math.pi * self.radius ** 3

    @property
    def area(self):
        return 4 * math.pi * self.radius ** 2

    def __str__(self):
        return f"Sphere with radius: {self.radius:.6f}"

    def __repr__(self):
        return f"Sphere({self.radius})"

    def __add__(self, other):
        if isinstance(other, Circle):
            return Sphere(self.radius + other.radius)
        return NotImplemented

    def __mul__(self, other):
        if isinstance(other, (int, float)):
            return Sphere(self.radius * other)
        return NotImplemented
