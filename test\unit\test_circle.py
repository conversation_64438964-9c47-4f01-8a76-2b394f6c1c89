import math
import unittest
from src import circle
from src import sphere


class TestCircle(unittest.TestCase):

    def setUp(self):
        print("Setting up!")
        self.CONST_TEST_RADIUS = 5
        self.CONST_TEST_DIAMETER = 8

    def tearDown(self):
        print("Tearing down!\n")

    def test_circle_creation(self):
        """Test that Circle can be created with a radius and has radius attribute"""
        # Test creating circle with radius
        c = circle.Circle(4)
        self.assertEqual(c.radius, 4)

        # Test creating circle with different radius
        c2 = circle.Circle(self.CONST_TEST_RADIUS)
        self.assertEqual(c2.radius, self.CONST_TEST_RADIUS)

        # Test that radius is required (should raise TypeError if not provided)
        with self.assertRaises(TypeError):
            circle.Circle()

    def test_circle_property_diameter(self):
        """Test that diameter property returns twice the radius"""
        c = circle.Circle(4)
        self.assertEqual(c.diameter, 8)

        c2 = circle.Circle(self.CONST_TEST_RADIUS)
        self.assertEqual(c2.diameter, self.CONST_TEST_RADIUS * 2)

    def test_circle_set_diameter(self):
        """Test that setting diameter updates radius accordingly"""
        c = circle.Circle(4)
        c.diameter = 2
        self.assertEqual(c.diameter, 2)
        self.assertEqual(c.radius, 1)

        # Test with another value
        c.diameter = self.CONST_TEST_DIAMETER
        self.assertEqual(c.diameter, self.CONST_TEST_DIAMETER)
        self.assertEqual(c.radius, self.CONST_TEST_DIAMETER / 2)

    def test_circle_area(self):
        """Test that area property returns correct area and cannot be set"""
        c = circle.Circle(2)
        expected_area = math.pi * 2 * 2  # π * r²
        self.assertAlmostEqual(c.area, expected_area, places=6)

        # Test that area cannot be set (should raise AttributeError)
        with self.assertRaises(AttributeError):
            c.area = 42

    def test_cls_method(self):
        """Test from_diameter classmethod creates Circle with correct radius and diameter"""
        c = circle.Circle.from_diameter(8)
        self.assertEqual(c.diameter, 8)
        self.assertEqual(c.radius, 4)

        # Test with constant
        c2 = circle.Circle.from_diameter(self.CONST_TEST_DIAMETER)
        self.assertEqual(c2.diameter, self.CONST_TEST_DIAMETER)
        self.assertEqual(c2.radius, self.CONST_TEST_DIAMETER / 2)

    def test_circle_str(self):
        """Test __str__ method returns proper string representation"""
        c = circle.Circle(4)
        expected_str = "Circle with radius: 4.000000"
        self.assertEqual(str(c), expected_str)

    def test_circle_repr(self):
        """Test __repr__ method returns evaluable representation"""
        c = circle.Circle(4)
        expected_repr = "Circle(4)"
        self.assertEqual(repr(c), expected_repr)

        # Test that eval(repr(c)) creates equivalent circle
        # Need to provide Circle in the namespace for eval
        d = eval(repr(c), {"Circle": circle.Circle})
        self.assertEqual(d.radius, c.radius)
        self.assertEqual(d.diameter, c.diameter)

    def test_circle_add(self):
        """Test adding two circles returns new circle with sum of radii"""
        c1 = circle.Circle(2)
        c2 = circle.Circle(4)
        c3 = c1 + c2
        self.assertEqual(c3.radius, 6)
        self.assertIsInstance(c3, circle.Circle)

    def test_circle_mult(self):
        """Test multiplying circle by number and number by circle"""
        c = circle.Circle(4)

        # Test c * number
        c2 = c * 3
        self.assertEqual(c2.radius, 12)
        self.assertIsInstance(c2, circle.Circle)

        # Test number * c (should work with __rmul__)
        c3 = 3 * c
        self.assertEqual(c3.radius, 12)
        self.assertIsInstance(c3, circle.Circle)

    def test_comparison_operators(self):
        """Test comparison operators work correctly for circles"""
        c1 = circle.Circle(2)
        c2 = circle.Circle(4)
        c3 = circle.Circle(4)

        # Test equality
        self.assertFalse(c1 == c2)
        self.assertTrue(c2 == c3)

        # Test less than and greater than
        self.assertTrue(c1 < c2)
        self.assertFalse(c1 > c2)
        self.assertTrue(c2 > c1)
        self.assertFalse(c2 < c1)

        # Test sorting
        circles = [circle.Circle(6), circle.Circle(2), circle.Circle(4), circle.Circle(1)]
        circles.sort()
        expected_radii = [1, 2, 4, 6]
        actual_radii = [c.radius for c in circles]
        self.assertEqual(actual_radii, expected_radii)

    def test_sphere_volume(self):
        """Test sphere volume property (4/3 * π * r³)"""
        s = sphere.Sphere(3)
        expected_volume = (4/3) * math.pi * 3 * 3 * 3
        self.assertAlmostEqual(s.volume, expected_volume, places=6)

    def test_sphere_area(self):
        """Test sphere surface area property (4 * π * r²)"""
        s = sphere.Sphere(2)
        expected_surface_area = 4 * math.pi * 2 * 2
        self.assertAlmostEqual(s.area, expected_surface_area, places=6)

    def test_sphere_add(self):
        """Test that sphere addition works and returns a Sphere"""
        s1 = sphere.Sphere(2)
        s2 = sphere.Sphere(3)
        s3 = s1 + s2
        self.assertEqual(s3.radius, 5)
        self.assertIsInstance(s3, sphere.Sphere)

    def test_sphere_comparison_operators(self):
        """Test that sphere comparison works"""
        s1 = sphere.Sphere(2)
        s2 = sphere.Sphere(4)
        self.assertTrue(s1 < s2)
        self.assertTrue(s2 > s1)

    def test_sphere_from_diameter(self):
        """Test that from_diameter creates a Sphere, not just a Circle"""
        s = sphere.Sphere.from_diameter(8)
        self.assertEqual(s.radius, 4)
        self.assertEqual(s.diameter, 8)
        self.assertIsInstance(s, sphere.Sphere)

    def test_sphere_str_repr(self):
        """Test sphere string representations"""
        s = sphere.Sphere(4)
        self.assertEqual(str(s), "Sphere with radius: 4.000000")
        self.assertEqual(repr(s), "Sphere(4)")


if __name__ == "__main__":
    unittest.main()
